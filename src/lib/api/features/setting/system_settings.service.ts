// import { PUBLIC_BACKEND_URL } from "$env/static/public";
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';

import type { SystemSettingInterface, SystemSettingResponse } from "../../types/system_setting"
import { ApiError } from "../../client/errors";

export class SystemSettingService {
    private baseUrl = `${getBackendUrl()}/setting`;

    async getAll(token: string): Promise<SystemSettingResponse> {
        try {
            const response = await fetch(`${this.baseUrl}/api/settings/`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            return {
                system_setting: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error fetching customers:', error);
            return {
                system_setting: [],
                res_status: error.status,
                error_msg: error instanceof Error ? error.message : 'Failed to fetch settings'
            };
        }
    }
    
    async uploadImage(key: string, imageFile: File, token: string): Promise<SystemSettingResponse> {
        try {
            const formData = new FormData();
            formData.append('key', key);
            formData.append('image_file', imageFile);

            const response = await fetch(`${this.baseUrl}/api/settings/image/`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    // Don't set Content-Type header here as it's set automatically with boundary for FormData
                },
                body: formData
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new ApiError(
                    JSON.stringify(errorData),
                    response.status
                );
            }

            const response_json = await response.json();
            const res_status = response.status;

            // console.log(response_json)
            return {
                upload_setting: response_json,
                res_status: res_status
            };
        } catch (error) {
            console.error('Error uploading image:', error);
            return {
                upload_setting: [],
                res_status: error instanceof ApiError ? error.status : 500,
                error_msg: error instanceof Error ? error.message : 'Failed to upload image'
            };
        }
    }
}
