<script lang="ts">
    import { enhance } from '$app/forms';
    import { <PERSON><PERSON>, <PERSON>dal, Alert, Checkbox } from 'flowbite-svelte';
    import { handleEnhance } from '$lib/components/Enhance/SubmissionHandleEnhance.svelte';

    // Expecting the current user and the list of tags as props.
    export let user: any;
    export let tags: any[];

    let userAssignTagForm: HTMLFormElement;
    let userAssignTagModalOpen = false;
    let currentUser: any = null;
    let selectedTagIds: (string | number)[] = [];
    
    let showSuccessMessage = false;
    let showErrorMessage = false;
    let successMessage = '';
    let errorMessage = '';

    // Open the modal and initialize with the current user's tags if available.
    function openUserAssignTagModal(user: any) {
        currentUser = { ...user };

        if (currentUser.tags && Array.isArray(currentUser.tags)) {
            selectedTagIds = currentUser.tags
                .map(tag => typeof tag === 'object' ? tag.id : tag)
                .filter(id => id !== undefined && id !== null && !isNaN(Number(id)));
        } else {
            selectedTagIds = [];
        }

        userAssignTagModalOpen = true;
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    function handleUserAssignTagSubmit(event: Event) {
        showSuccessMessage = false;
        showErrorMessage = false;
        successMessage = '';
        errorMessage = '';
    }

    $: enhanceOptions = {
        modalOpen: userAssignTagModalOpen,
        setModalOpen: (value: boolean) => userAssignTagModalOpen = value,
        setShowSuccessMessage: (value: boolean) => showSuccessMessage = value,
        setSuccessMessage: (value: string) => successMessage = value,
        setShowErrorMessage: (value: boolean) => showErrorMessage = value,
        setErrorMessage: (value: string) => errorMessage = value
    };

    $: tagOptions = tags.map(tag => ({
        value: tag.id,
        name: `${tag.name}`
    }));
</script>

<!-- Button to open the assign tag modal -->
<Button  
    color="none"
    class="w-full justify-start text-left hover:bg-gray-100 p-0"
    on:click={() => openUserAssignTagModal(user)}>
    Assign Tags
</Button>

<!-- Modal for assigning tags -->
<Modal bind:open={userAssignTagModalOpen} size="md" autoclose={false} class="w-full">
    <h2 slot="header">Assign Tags</h2>
    {#if currentUser}
        {#if showSuccessMessage}
            <Alert color="green" class="mb-4">
                {successMessage}
            </Alert>
        {/if}
        {#if showErrorMessage}
            <Alert color="red" class="mb-4">
                {errorMessage}
            </Alert>
        {/if}
        <form 
            bind:this={userAssignTagForm} 
            action="?/assign_user_tag" 
            method="POST" 
            use:enhance={() => handleEnhance(enhanceOptions)}
            on:submit={handleUserAssignTagSubmit}
        >
            <input type="hidden" name="id" value={currentUser.id}>
            <div class="min-h-[200px]">
                <label for="SelectedTags" class="block text-sm font-medium text-gray-700 mb-1 text-left">
                    Select User's Tags
                </label>
                {#each tagOptions as tag (tag.value)}
                    <div class="mb-2">
                        <Checkbox 
                            checked={selectedTagIds.includes(tag.value)} 
                            value={tag.value}
                            on:change={() => {
                                if (selectedTagIds.includes(tag.value)) {
                                    selectedTagIds = selectedTagIds.filter(id => id !== tag.value);
                                } else {
                                    selectedTagIds = [...selectedTagIds, tag.value];
                                }
                            }}
                        >
                            {tag.name}
                        </Checkbox>
                    </div>
                {/each}
                <input type="hidden" name="tag_ids[]" value={selectedTagIds}>
            </div>
        </form>
    {/if}
    <svelte:fragment slot="footer">
        <Button color="blue" on:click={() => userAssignTagForm.requestSubmit()}>Confirm</Button>
        <Button color="none" on:click={() => userAssignTagModalOpen = false}>Cancel</Button>
    </svelte:fragment>
</Modal>
