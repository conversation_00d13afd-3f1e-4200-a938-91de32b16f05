<script lang="ts">
	import { language } from '$lib/stores/i18n';

	const languages = [
		{ code: 'en', name: 'English' },
		{ code: 'th', name: 'Thai' }
	];
</script>

<div class="flex gap-1 justify-start">
	{#each languages as lang}
		<button
			class={`px-2 py-1 rounded text-xs transition
				${$language === lang.code
					? 'bg-gray-800 text-white font-bold'
					: 'bg-gray-300 text-white border border-white'}`}
			on:click={() => {
				language.set(lang.code);
				setTimeout(() => location.reload(), 50);
			}}
		>
			{lang.name}
		</button>
	{/each}
</div>
