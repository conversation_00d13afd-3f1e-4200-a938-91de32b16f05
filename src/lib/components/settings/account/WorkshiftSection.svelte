<script>
    import { t } from '$lib/stores/i18n';
    import { onMount } from 'svelte';
    import { Alert, Toggle, Checkbox, Button, Select, Label } from 'flowbite-svelte';
  
    export let dominantColor;
    export let user_schedule;
    export let business_hours;
  
    // our reactive state
    let days = [];
    let sameAsBusinessHours = false;
  
    // build your time dropdown
    const timeOptions = Array.from({ length: 48 }, (_, i) => {
      const hour = Math.floor(i/2).toString().padStart(2, '0');
      const minutes = i % 2 === 0 ? '00' : '30';
      return `${hour}:${minutes}`;
    });
  
    // on mount, pull in the hours & toggle state
    onMount(() => {
      if (business_hours?.workShift) {
        days = JSON.parse(JSON.stringify(business_hours.workShift));
      }
      sameAsBusinessHours = user_schedule?.sameAsBusinessHours ?? false;
    });
  
    function toggleDay(index) {
      days[index].active = !days[index].active;
      days = [...days]; // force reactivity
    }
  
    function toggleSameAsBusinessHours() {
      if (sameAsBusinessHours && business_hours?.workShift) {
        // swap in the biz‐hours copy
        days = JSON.parse(JSON.stringify(business_hours.workShift));
      }
      // otherwise we leave whatever the user has in `days`
    }
  </script>
  
  <form
    method="POST"
    action="?/update_user_work_schedule"
    class="space-y-4 p-6 bg-white rounded-lg shadow-md"
  >
    <div class="flex items-center justify-between w-full">
      <div>
        <h2 class="text-xl font-medium text-gray-700">
          {t('work_shift_title')}
        </h2>
        <p class="text-sm text-gray-600">
          {t('work_shift_description_1')}
        </p>
        <p class="text-sm text-gray-600">
          {t('work_shift_description_2')}
        </p>
      </div>
  
      <!-- submit as a real form -->
      <Button type="submit"
              class="bg-blue-500 hover:bg-blue-600 text-white font-medium rounded-lg transition-colors">
        {t('save')}
      </Button>
    </div>
  
    <div class="flex items-center mb-8">
      <span class="mr-2">{t('same_as_business_hours')}</span>
      <Toggle color="blue"
              bind:checked={sameAsBusinessHours}
              on:change={toggleSameAsBusinessHours} />
    </div>
  
    <!-- these hidden fields will POST your JSON -->
    <input type="hidden"
           name="sameAsBusinessHours"
           value={sameAsBusinessHours} 
    />

    <input
        type="hidden"
        name="workShiftData"
        value={JSON.stringify(
            sameAsBusinessHours && business_hours?.workShift
            ? business_hours.workShift
            : days
        )}
        />

    <!-- <Alert color="yellow">
        <span class="font-medium">Check!</span>
        {sameAsBusinessHours}
        {JSON.stringify(
            sameAsBusinessHours
            ? business_hours.workShift
            : days
        )}
    </Alert> -->

    {#if !sameAsBusinessHours}
        <div>
            {#each days as day, i}
            <div class="flex items-center mb-4">
                <div class="w-10">
                <input
                    type="checkbox"
                    id="day-{i}"
                    checked={day.active}
                    on:change={() => toggleDay(i)}
                    class="w-5 h-5 text-blue-500 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                </div>
                <label for="day-{i}" class="w-32 font-medium text-gray-700">
                {day.day}
                </label>
    
                {#if day.active}
                <div class="flex flex-1 items-center">
                    <div class="relative inline-block w-40">
                    <select bind:value={day.times[0].start}
                            class="block appearance-none w-full bg-white border border-gray-300 text-gray-700 py-2 px-4 pr-8 rounded leading-tight focus:outline-none focus:border-blue-500">
                        {#each timeOptions as option}
                        <option value={option}>{option}</option>
                        {/each}
                    </select>
                    </div>
    
                    <span class="mx-3">{t('to')}</span>
    
                    <div class="relative inline-block w-40">
                    <select bind:value={day.times[0].end}
                            class="block appearance-none w-full bg-white border border-gray-300 text-gray-700 py-2 px-4 pr-8 rounded leading-tight focus:outline-none focus:border-blue-500">
                        {#each timeOptions as option}
                        <option value={option}>{option}</option>
                        {/each}
                    </select>
                    </div>
                </div>
                {/if}
            </div>
            {/each}
        </div>
    {/if}
  </form>
  