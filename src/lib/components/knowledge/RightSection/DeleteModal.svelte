<script lang="ts">
	import { enhance } from '$app/forms';
	import { Button, Modal } from 'flowbite-svelte';
	import { ExclamationCircleOutline } from 'flowbite-svelte-icons';

	export let deleteModal = false;
	// export let closeModal: () => void;
	export let selectedDocuments = [];
    export let isActive = true;

	let deleteForm: HTMLFormElement;

	function handleDeleteButtonClick() {
		deleteForm.requestSubmit();
	}
</script>

<Modal bind:open={deleteModal} size="xs" autoclose>
	<div class="text-center">
		<ExclamationCircleOutline class="mx-auto mb-4 h-12 w-12 text-gray-400 dark:text-gray-200" />
		<form
			bind:this={deleteForm}
			action="?/delete_document"
			method="POST"
			use:enhance={() => {
				return async ({ update }) => {
					await update();
				};
			}}
		>
			<h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
				Are you sure you want to delete {selectedDocuments.size}
				{selectedDocuments.size > 1 ? 'documents' : 'document'}
                {!isActive ? 'permanently': ''}?
			</h3>
			<input type="hidden" name="document_ids" value={Array.from(selectedDocuments).join(',')} />
			<Button color="alternative">No</Button>
			<Button color="red" class="me-2" on:click={handleDeleteButtonClick}>Yes</Button>
		</form>
	</div>
</Modal>
