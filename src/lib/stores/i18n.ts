// src/stores/i18n.ts
import { writable, derived, get } from 'svelte/store';
import { browser } from '$app/environment';
import en from '$lib/locales/en.json';
import th from '$lib/locales/th.json';

const dictionaries = { en, th } as const;

/* ------------ language store ------------ */
const stored = browser && (localStorage.getItem('lang') as 'en' | 'th' | null);
export const language = writable<'en' | 'th'>(stored ?? 'en');

if (browser) {
	language.subscribe((v) => localStorage.setItem('lang', v));
}

/* ------------ active dictionary ------------ */
export const dict = derived(language, ($l) => dictionaries[$l] ?? dictionaries.en);

/* ------------ helper ------------ */
export function t(key: string): string {
	const d = get(dict);
	return (d && (d as Record<string, string>)[key]) ?? key;
}