// import { type PageServerLoad } from "./$types";
// import { 
//     PUBLIC_LLM_FAQ_URL, 
//     PUBLIC_LLM_RECOMMENDATION_URL, 
//     PUBLIC_LLM_DEFAULT_URL, 
//     PUBLIC_LLM_INFORMATION_URL 
// } from "$env/static/public";

// export const load = () => {
//     return {
//         PUBLIC_LLM_FAQ_URL: PUBLIC_LLM_FAQ_URL,
// 		PUBLIC_LLM_RECOMMENDATION_URL: PUBLIC_LLM_RECOMMENDATION_URL,
// 		PUBLIC_LLM_DEFAULT_URL: PUBLIC_LLM_DEFAULT_URL,
//         PUBLIC_LLM_INFORMATION_URL : PUBLIC_LLM_INFORMATION_URL
//     };
//   };

// import { env as publicEnv } from '$env/dynamic/public';
import { env as privateEnv } from '$env/dynamic/private';

export const load = () => {
    return {
        // PUBLIC_LLM_FAQ_URL:             publicEnv.PUBLIC_LLM_FAQ_URL,
        // PUBLIC_LLM_RECOMMENDATION_URL:  publicEnv.PUBLIC_LLM_RECOMMENDATION_URL,
        // PUBLIC_LLM_DEFAULT_URL:         publicEnv.PUBLIC_LLM_DEFAULT_URL,
        // PUBLIC_LLM_INFORMATION_URL:     publicEnv.PUBLIC_LLM_INFORMATION_URL
        PRIVATE_LLM_INFORMATION_URL:    privateEnv.PRIVATE_LLM_INFORMATION_URL
    };     
};