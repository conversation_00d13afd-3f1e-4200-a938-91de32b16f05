import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { initUser, user } from '../../../../lib/stores/user';

// import { PUBLIC_BACKEND_URL } from '$env/static/public';
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';


export const GET: RequestHandler = async ({ url, cookies }) => {
    try {
        const newStatus = url.searchParams.get("status");  // ✅ get from query param
        // Get the tokens before deleting cookies
        const accessToken = cookies.get("access_token");   

        // Call backend logout endpoint
        const response = await fetch(`${getBackendUrl()}/user/api/user-status/update/`, {
            method: 'PATCH',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            body: JSON.stringify({
                new_status: newStatus
            })
        });

        if (!response.ok) {
            throw new Error('Logout failed');
        }

        return json({
            "message":"ok",
            "new_status": newStatus

        });
    } catch (error) {
        console.error('Update my status error:', error);
        return json({"message":"error", "details": error.message}, {status: 500});
    }
};