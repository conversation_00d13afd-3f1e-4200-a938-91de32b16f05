import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';

// import { PUBLIC_BACKEND_URL } from '$env/static/public';
// import { env as publicEnv } from '$env/dynamic/public';
import { getBackendUrl } from '$src/lib/config';


export const GET: RequestHandler = async ({ url, cookies }) => {
    try {
        // Get the tokens before deleting cookies
        const accessToken = cookies.get("access_token");

        // Call backend logout endpoint
        const response = await fetch(`${getBackendUrl()}/setting/api/settings/`, {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${accessToken}`,
                'Content-Type': 'application/json'
            }
        });
        if (!response.ok) {
            throw new Error('Get setting failed');
        }

        const response_json = await response.json();
        const res_status = response.status;
        // console.log(response_json)
        return json({
            "message":"ok",
            "system_settings": response_json

        });
    } catch (error) {
        console.error('Get setting error:', error);
        return json({"message":"error", "details": error.message}, {status: 500});
    }
};