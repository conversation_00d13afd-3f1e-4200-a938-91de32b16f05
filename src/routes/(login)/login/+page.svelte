<script lang="ts">
  import type { PageData } from "./$types";
  import type { Response } from "./schema";
  import LoginForm from "./login-form.svelte";
  import { Card } from 'flowbite-svelte';
  import SuperDebug from "sveltekit-superforms";
  import { onMount } from 'svelte';
  
  export let data: PageData;
  export let form: Response;
  
  let mounted = false;
  
  onMount(() => {
    // Add a delay to ensure styles are loaded (optional)
    setTimeout(() => {
      mounted = true;
    }, 500);
  });
</script>

<!-- <div class="h-screen flex">
  <div class="m-auto relative">

    <div class="absolute -rotate-6 -translate-x-2 translate-y-2 w-full h-full bg-blue-500 rounded-lg"></div>

    <div class="relative">
      <Card>
        <div style="width: 600px;"></div>
        <LoginForm data={data.form} response={form} />
      </Card>
    </div>
  </div>
</div> -->

{#if !mounted}
  <div class="min-h-screen flex items-center justify-center">
    <div class="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-cyan-400"></div>
  </div>
{:else}
  <div class="min-h-screen bg-gray-100 py-6 flex flex-col justify-center sm:py-12">
    <div class="relative py-3 sm:max-w-xl sm:mx-auto">
      <div
        class="absolute inset-0 bg-gradient-to-r from-cyan-400 to-sky-500 shadow-lg transform -skew-y-6 sm:skew-y-0 sm:-rotate-6 sm:rounded-3xl">
      </div>
      <div class="relative px-4 py-10 bg-white shadow-lg sm:rounded-3xl sm:p-20">
        <div class="relative">
          <Card class="!border-none !shadow-none">
            <div style="width: 600px;"></div>
            <LoginForm data={data.form} response={form} />
          </Card>
        </div>
      </div>
    </div>
  </div>
{/if}


<!-- <div class="min-h-screen flex relative">
  <div class="absolute inset-0 bg-[url('/images/login-page-image.jpg')] bg-cover bg-center bg-no-repeat"></div>
  
  <div class="m-auto relative z-10">
    <div class="backdrop-blur-md bg-white/30 rounded-lg shadow-xl border border-white/20">
      <div class="w-[500px] p-6">
        <LoginForm data={data.form} response={form} />
      </div>
    </div>
  </div>
</div>

<style>
  :global(body) {
    margin: 0;
    padding: 0;
  }
</style> -->