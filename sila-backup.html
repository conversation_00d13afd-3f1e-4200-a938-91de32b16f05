<!-- <PERSON><PERSON>'s Backup -->

<!-- Policy Card -->

<!-- Policy details -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 justify-start mb-4">
    {#each visiblePolicies as policy, index}
    <div class="w-full max-w-sm rounded-xl bg-white shadow-md p-6 text-left">
        <h2 class="text-xl font-bold text-gray-800 mb-4">
            {t('policy_details')} #{index + 1}
        </h2>
        <div class="space-y-4 text-sm">
            <!-- Product Name -->
            <div class="flex justify-between border-b pb-1">
                <span class="text-gray-500">{t('product_name')}</span>
                <span class="font-medium text-gray-800">{policy.product?.name ?? 'N/A'}</span>
            </div>

            <!-- Product Type -->
            <div class="flex justify-between border-b pb-1">
                <span class="text-gray-500">{t('product_type')}</span>
                <span class="font-medium text-gray-800">{policy.product?.product_type ?? 'N/A'}</span>
            </div>

            <!-- Status -->
            <div class="flex justify-between border-b pb-1">
                <span class="text-gray-500">{t('status')}</span>
                <span class={`font-semibold ${policy.policy_status==='ACTIVE' ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                    {policy.policy_status}
                </span>
            </div>

            <!-- Issue Date -->
            <div class="flex justify-between border-b pb-1">
                <span class="text-gray-500">{t('issue_date')}</span>
                <span class="font-medium text-gray-800">{displayDateDraft(policy.issue_date)}</span>
            </div>

            <!-- Start Date -->
            <div class="flex justify-between border-b pb-1">
                <span class="text-gray-500">{t('start_date')}</span>
                <span class="font-medium text-gray-800">{displayDateDraft(policy.start_date)}</span>
            </div>

            <!-- End Date -->
            <div class="flex justify-between">
                <span class="text-gray-500">{t('end_date')}</span>
                <span class="font-medium text-gray-800">{displayDateDraft(policy.end_date)}</span>
            </div>
        </div>
    </div>
    {/each}
</div>